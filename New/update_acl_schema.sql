-- <PERSON><PERSON>t to Update Database Schema for PXE ACL Integration
-- Run this script to add the required ACL tracking columns

-- Step 1: Add ACL tracking columns to pxe_reinstall_sessions table
-- Note: Run each ALTER TABLE statement separately to handle existing columns gracefully

-- Add acl_removed column
ALTER TABLE `pxe_reinstall_sessions`
ADD COLUMN `acl_removed` tinyint(1) DEFAULT 0 COMMENT 'Whether ACLs were removed at start of reinstall';

-- Add acl_reapplied column
ALTER TABLE `pxe_reinstall_sessions`
ADD COLUMN `acl_reapplied` tinyint(1) DEFAULT 0 COMMENT 'Whether ACLs were reapplied after completion';

-- Add acl_removal_error column
ALTER TABLE `pxe_reinstall_sessions`
ADD COLUMN `acl_removal_error` text DEFAULT NULL COMMENT 'Error message from ACL removal operation';

-- Add acl_reapply_error column
ALTER TABLE `pxe_reinstall_sessions`
ADD COLUMN `acl_reapply_error` text DEFAULT NULL COMMENT 'Error message from ACL reapplication operation';

-- Step 2: Verify the columns were added successfully
SELECT 'Checking ACL columns...' as status;
SHOW COLUMNS FROM pxe_reinstall_sessions LIKE 'acl_%';

-- Step 3: Display the complete table structure
SELECT 'Complete table structure:' as status;
DESCRIBE pxe_reinstall_sessions;

-- Alternative: If the table doesn't exist, create it with all columns
-- (Uncomment the following if you need to create the table from scratch)

/*
CREATE TABLE IF NOT EXISTS `pxe_reinstall_sessions` (
    `id` int NOT NULL AUTO_INCREMENT,
    `server_id` int NOT NULL,
    `server_type` enum('dedicated','blade') NOT NULL DEFAULT 'dedicated',
    `server_label` varchar(255) NOT NULL,
    `mac_address` varchar(17) NOT NULL,
    `ip_address` varchar(45) NOT NULL,
    `hostname` varchar(255) NOT NULL,
    `os_template` varchar(100) NOT NULL,
    `status` enum('pending','active','completed','failed','cancelled') NOT NULL DEFAULT 'pending',
    `dhcp_configured` tinyint(1) DEFAULT 0,
    `files_created` tinyint(1) DEFAULT 0,
    `acl_removed` tinyint(1) DEFAULT 0,
    `acl_reapplied` tinyint(1) DEFAULT 0,
    `acl_removal_error` text DEFAULT NULL,
    `acl_reapply_error` text DEFAULT NULL,
    `started_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `completed_at` timestamp NULL DEFAULT NULL,
    `error_message` text,
    `network_config` json,
    PRIMARY KEY (`id`),
    KEY `idx_server` (`server_id`,`server_type`),
    KEY `idx_status` (`status`),
    KEY `idx_started` (`started_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
*/

-- Step 4: Test query to verify ACL columns work
SELECT 'Testing ACL columns...' as status;
SELECT 
    id, 
    server_id, 
    server_type, 
    status,
    acl_removed,
    acl_reapplied,
    acl_removal_error,
    acl_reapply_error
FROM pxe_reinstall_sessions 
LIMIT 5;

SELECT 'Schema update complete!' as status;
