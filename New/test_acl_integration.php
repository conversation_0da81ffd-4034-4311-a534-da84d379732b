<?php
/**
 * Test script for PXE ACL Integration
 * 
 * This script tests the ACL management functions without actually
 * performing a PXE reinstall. Use this to validate the integration.
 */

require_once 'mysql.php';
require_once 'pxe_api_integration.php';

// Test configuration
$TEST_SERVER_ID = 1; // Change this to a valid server ID
$TEST_SERVER_TYPE = 'dedicated'; // or 'blade'

echo "=== PXE ACL Integration Test ===\n\n";

try {
    // Initialize the PXE manager
    $manager = new PXENetworkManager($pdo);
    
    echo "1. Testing ACL Removal Function\n";
    echo "--------------------------------\n";
    
    $acl_removal_result = $manager->removeServerACLs($TEST_SERVER_ID, $TEST_SERVER_TYPE);
    
    echo "ACL Removal Result:\n";
    echo "- Success: " . ($acl_removal_result['success'] ? 'Yes' : 'No') . "\n";
    echo "- ACL Removed: " . ($acl_removal_result['acl_removed'] ? 'Yes' : 'No') . "\n";
    echo "- Message: " . $acl_removal_result['message'] . "\n";
    
    if (isset($acl_removal_result['error'])) {
        echo "- Error: " . $acl_removal_result['error'] . "\n";
    }
    
    if (isset($acl_removal_result['warning'])) {
        echo "- Warning: " . $acl_removal_result['warning'] . "\n";
    }
    
    echo "\n2. Testing ACL Reapplication Function\n";
    echo "-------------------------------------\n";
    
    $acl_reapply_result = $manager->reapplyServerACLs($TEST_SERVER_ID, $TEST_SERVER_TYPE);
    
    echo "ACL Reapplication Result:\n";
    echo "- Success: " . ($acl_reapply_result['success'] ? 'Yes' : 'No') . "\n";
    echo "- ACL Applied: " . ($acl_reapply_result['acl_applied'] ? 'Yes' : 'No') . "\n";
    echo "- Message: " . $acl_reapply_result['message'] . "\n";
    
    if (isset($acl_reapply_result['error'])) {
        echo "- Error: " . $acl_reapply_result['error'] . "\n";
    }
    
    if (isset($acl_reapply_result['warning'])) {
        echo "- Warning: " . $acl_reapply_result['warning'] . "\n";
    }
    
    if (isset($acl_reapply_result['subnets_configured'])) {
        echo "- Subnets Configured: " . $acl_reapply_result['subnets_configured'] . "\n";
    }
    
    echo "\n3. Testing Server Information Retrieval\n";
    echo "----------------------------------------\n";
    
    // Use reflection to access private method for testing
    $reflection = new ReflectionClass($manager);
    $getServerInfoMethod = $reflection->getMethod('getServerInfo');
    $getServerInfoMethod->setAccessible(true);
    
    $server_info = $getServerInfoMethod->invoke($manager, $TEST_SERVER_ID, $TEST_SERVER_TYPE);
    
    if ($server_info) {
        echo "Server Information:\n";
        echo "- ID: " . $server_info['id'] . "\n";
        echo "- Label: " . ($server_info['label'] ?? 'Not set') . "\n";
        echo "- Switch IP: " . ($server_info['switch_ip'] ?? 'Not configured') . "\n";
        echo "- Port: " . ($server_info['port1'] ?? 'Not configured') . "\n";
        echo "- MAC: " . ($server_info['mac'] ?? 'Not set') . "\n";
        echo "- Main IP: " . ($server_info['main_ip'] ?? 'Not set') . "\n";
    } else {
        echo "Server not found or error retrieving server information.\n";
    }
    
    echo "\n4. Testing Subnet Retrieval\n";
    echo "----------------------------\n";
    
    $getServerSubnetsMethod = $reflection->getMethod('getServerSubnets');
    $getServerSubnetsMethod->setAccessible(true);
    
    $subnets = $getServerSubnetsMethod->invoke($manager, $TEST_SERVER_ID, $TEST_SERVER_TYPE);
    
    if (!empty($subnets)) {
        echo "Assigned Subnets:\n";
        foreach ($subnets as $subnet) {
            echo "- " . $subnet . "\n";
        }
    } else {
        echo "No subnets assigned to this server.\n";
    }
    
    echo "\n5. Testing Database Schema\n";
    echo "--------------------------\n";
    
    // Check if ACL columns exist in pxe_reinstall_sessions
    $stmt = $pdo->prepare("SHOW COLUMNS FROM pxe_reinstall_sessions LIKE 'acl_%'");
    $stmt->execute();
    $acl_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($acl_columns)) {
        echo "ACL tracking columns found:\n";
        foreach ($acl_columns as $column) {
            echo "- " . $column['Field'] . " (" . $column['Type'] . ")\n";
        }
    } else {
        echo "ACL tracking columns not found. Database schema may need updating.\n";
    }
    
    echo "\n=== Test Complete ===\n";
    echo "\nNotes:\n";
    echo "- This test does not perform actual switch operations\n";
    echo "- Check the logs (auto.logs) for detailed operation information\n";
    echo "- Ensure server has proper switch configuration for full functionality\n";
    echo "- Test with a server that has subnet assignments for complete testing\n";
    
} catch (Exception $e) {
    echo "Test failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n";
?>
