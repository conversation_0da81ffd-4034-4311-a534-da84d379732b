# PXE Reinstall ACL Integration

## Overview

This document describes the ACL (Access Control List) management integration with the PXE Reinstall process. The system automatically manages network switch ACLs during server reinstalls to ensure proper network security throughout the reinstall lifecycle.

## Features

### 1. **Automatic ACL Removal**
- ACLs are automatically removed from network switches when a PXE reinstall begins
- Ensures clean network access during the reinstall process
- Prevents conflicts with existing network configurations

### 2. **Automatic ACL Reapplication**
- ACLs are automatically reapplied when the PXE reinstall completes successfully
- Restores network security policies after reinstall
- Uses current subnet assignments for ACL configuration

### 3. **Comprehensive Error Handling**
- ACL operations don't block the PXE reinstall process if they fail
- Detailed logging of all ACL operations
- Database tracking of ACL status throughout the process

### 4. **Status Tracking**
- Real-time ACL status information in the PXE Reinstall Modal
- Database fields track ACL removal and reapplication status
- Error messages stored for troubleshooting

## How It Works

### Process Flow

1. **PXE Reinstall Initiated**
   - User starts PXE reinstall through the modal
   - System reserves installation slot
   - **ACL Removal Phase begins**

2. **ACL Removal Phase**
   - System identifies server's switch configuration
   - Retrieves all assigned subnets for the server
   - Removes existing ACL rules from the switch interface
   - Updates database with removal status

3. **PXE Installation**
   - Normal PXE reinstall process continues
   - DHCP configuration, file creation, IPMI commands
   - Server boots and installs new OS

4. **Installation Completion**
   - Cleanup process is triggered
   - Session marked as completed
   - **ACL Reapplication Phase begins**

5. **ACL Reapplication Phase**
   - System retrieves current subnet assignments
   - Creates new ACL rules based on current subnets
   - Applies ACL rules to the switch interface
   - Updates database with reapplication status

## Database Schema Changes

### pxe_reinstall_sessions Table

New columns added:
```sql
ALTER TABLE `pxe_reinstall_sessions` 
ADD COLUMN `acl_removed` tinyint(1) DEFAULT 0,
ADD COLUMN `acl_reapplied` tinyint(1) DEFAULT 0,
ADD COLUMN `acl_removal_error` text DEFAULT NULL,
ADD COLUMN `acl_reapply_error` text DEFAULT NULL;
```

## API Changes

### Enhanced check_installation_status Endpoint

Now includes ACL status information:
```json
{
  "installation_in_progress": true,
  "session_id": 123,
  "os_name": "Ubuntu 22.04 LTS",
  "status": "active",
  "started_at": "2024-01-15 10:30:00",
  "server_label": "Server-001",
  "error_message": null,
  "acl_status": {
    "removed": true,
    "reapplied": false,
    "removal_error": null,
    "reapply_error": null
  }
}
```

## New Functions

### PXENetworkManager Class

#### `removeServerACLs($server_id, $server_type)`
- Removes ACL rules for a server before PXE reinstall
- Returns status information and error details
- Integrates with existing ACL removal functions

#### `reapplyServerACLs($server_id, $server_type)`
- Reapplies ACL rules after PXE reinstall completion
- Uses current subnet assignments
- Returns status information and error details

#### Helper Functions
- `getServerInfo($server_id, $server_type)` - Retrieves server and switch details
- `getServerSubnets($server_id, $server_type)` - Gets assigned subnets
- `executeACLRemovalCommands($switchIp, $password, $commands)` - SSH execution
- `executeACLCommands($switchIp, $password, $commands)` - SSH execution

## Frontend Changes

### PXE Reinstall Modal

Enhanced to show ACL status during installation:
- Visual indicators for ACL removal/reapplication status
- Error messages for ACL operations
- Real-time status updates

## Error Handling

### Graceful Degradation
- ACL operation failures don't stop the PXE reinstall process
- Warnings logged for manual intervention if needed
- Status tracked in database for troubleshooting

### Logging
All ACL operations are logged to `auto.logs`:
- ACL removal attempts and results
- ACL reapplication attempts and results
- Error conditions and warnings
- Switch connection issues

## Requirements

### Server Configuration
- Server must have switch_id and port1 configured
- Switch must be configured with IP address and credentials
- Server must have subnet assignments for ACL creation

### Switch Requirements
- SSH access enabled with admin credentials
- Support for Cisco-style ACL commands
- Network connectivity from the management server

## Troubleshooting

### Common Issues

1. **ACL Removal Failed**
   - Check switch connectivity and credentials
   - Verify server has switch configuration
   - Check logs for specific error messages

2. **ACL Reapplication Failed**
   - Ensure server has subnet assignments
   - Verify switch is accessible after reinstall
   - Check for network configuration changes

3. **No ACL Operations**
   - Server may not have switch configuration
   - No subnets assigned to server
   - Switch credentials may be missing

### Manual Intervention

If ACL operations fail, manual switch configuration may be required:
1. Connect to switch via SSH
2. Remove old ACL rules: `no ip access-list extended <interface>`
3. Create new ACL rules based on current subnet assignments
4. Apply ACL to interface: `ip access-group <acl-name> in`

## Security Considerations

- ACL removal temporarily reduces network security
- Reinstall process should be monitored
- Manual verification recommended for critical servers
- Switch credentials stored securely in database

## Integration Points

The ACL management integrates with:
- Existing ACL functions in `api_admin_subnets.php`
- PXE reinstall workflow in `pxe_api_integration.php`
- Switch configuration system
- Subnet management system
- Frontend PXE Reinstall Modal

## Future Enhancements

Potential improvements:
- ACL backup and restore functionality
- Custom ACL rules during reinstall
- Integration with network monitoring systems
- Automated ACL verification after reapplication
