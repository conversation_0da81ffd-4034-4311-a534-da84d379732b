<?php
require_once("auth_functions.php");

// Helper function to migrate Open status to In Progress
function migrateOpenStatus($pdo) {
  try {
    $pdo->exec("UPDATE tasks SET status = 'In Progress' WHERE status = 'Open'");
  } catch (Exception $e) {
    error_log("Failed to migrate Open status: " . $e->getMessage());
  }
}

// Run migration on first load
if (isset($pdo)) {
  migrateOpenStatus($pdo);
}

if($_GET['f'] == 'get_dashboard_tasks'){
  try {
    // Authenticate user
    $admin_id = auth_admin();

    // Get sorting parameters
    $sortBy = isset($_GET['sortBy']) ? $_GET['sortBy'] : 'id';
    $sortField = isset($_GET['sortField']) ? $_GET['sortField'] : 'id';
    $sortDirection = isset($_GET['sortDirection']) ? $_GET['sortDirection'] : 'desc';

    // Get filter parameters
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $category = isset($_GET['category']) ? $_GET['category'] : '';
    $search = isset($_GET['search']) ? $_GET['search'] : '';
    $showCompleted = isset($_GET['showCompleted']) && $_GET['showCompleted'] === 'true';

    // Check if admin_departments table exists
    $tablesResult = $pdo->query("SHOW TABLES");
    $tables = $tablesResult->fetchAll(PDO::FETCH_COLUMN);
    $adminDeptTableExists = in_array('admin_departments', $tables);

    // Get admin's primary department
    $adminDeptQuery = $pdo->prepare("SELECT department_id FROM admins WHERE id = :admin_id");
    $adminDeptQuery->bindValue(':admin_id', $admin_id);
    $adminDeptQuery->execute();
    $adminDept = $adminDeptQuery->fetch(PDO::FETCH_ASSOC);
    $primaryDeptId = $adminDept ? $adminDept['department_id'] : null;

    // Get admin's assigned departments if the table exists
    $assignedDeptIds = [];
    if ($adminDeptTableExists) {
      $assignedDeptsQuery = $pdo->prepare("SELECT department_id FROM admin_departments WHERE admin_id = :admin_id");
      $assignedDeptsQuery->bindValue(':admin_id', $admin_id);
      $assignedDeptsQuery->execute();
      while ($dept = $assignedDeptsQuery->fetch(PDO::FETCH_ASSOC)) {
        $assignedDeptIds[] = $dept['department_id'];
      }
    }

    // If primary department exists, add it to assigned departments
    if ($primaryDeptId && !in_array($primaryDeptId, $assignedDeptIds)) {
      $assignedDeptIds[] = $primaryDeptId;
    }

    error_log("Admin $admin_id has access to department IDs: " . implode(", ", $assignedDeptIds));

    // Prepare the base query with last message subquery
    $sql = "SELECT t.*,
           d.department_name,
           a.id as assigned_admin_id,
           a.first_name as assigned_first_name,
           a.last_name as assigned_last_name,
           (SELECT MAX(created_at) FROM task_messages WHERE task_id = t.id) as last_message_time
           FROM tasks t
           LEFT JOIN departments d ON t.department_id = d.id
           LEFT JOIN admins a ON t.admin_id = a.id
           WHERE 1=1";

    // Filter by department if we have assigned departments
    if (!empty($assignedDeptIds)) {
      $departmentConditions = [];
      foreach ($assignedDeptIds as $deptId) {
        $departmentConditions[] = "t.department_id = " . intval($deptId);
      }
      // Also include tasks with no department assigned (NULL department_id)
      $departmentConditions[] = "t.department_id IS NULL";
      $sql .= " AND (" . implode(" OR ", $departmentConditions) . ")";
      error_log("Filtering tasks by department IDs: " . implode(", ", $assignedDeptIds) . " (plus NULL)");
    } else {
      // If admin has no assigned departments, only show tasks with no department
      $sql .= " AND t.department_id IS NULL";
      error_log("Admin $admin_id has no assigned departments. Showing only tasks with no department.");
    }

    // Add filters
    if ($status && $status != 'All') {
      $sql .= " AND t.status = " . $pdo->quote($status);
    }

    if ($category && $category != 'All') {
      $sql .= " AND t.category = " . $pdo->quote($category);
    }

    if ($search) {
      $sql .= " AND (t.name LIKE " . $pdo->quote("%$search%") .
              " OR t.content LIKE " . $pdo->quote("%$search%") .
              " OR t.id LIKE " . $pdo->quote("%$search%") .
              " OR d.department_name LIKE " . $pdo->quote("%$search%") . ")";
    }

    // Filter completed tasks if showCompleted is false
    if (!$showCompleted) {
      $sql .= " AND t.status NOT IN ('Completed', 'Closed', 'Resolved')";
    }
    
    // Add sorting based on sortBy parameter
    if ($sortBy === 'lastUpdated') {
      // Sort by last message time, with tasks without messages at the end
      $sql .= " ORDER BY COALESCE(last_message_time, t.datetime) " . ($sortDirection === 'asc' ? 'ASC' : 'DESC');
    } else {
      // Regular field sorting
      $allowedSortFields = ['id', 'name', 'status', 'priority', 'datetime'];
      if (in_array($sortField, $allowedSortFields)) {
        $sql .= " ORDER BY t.$sortField " . ($sortDirection === 'asc' ? 'ASC' : 'DESC');
      } else {
        $sql .= " ORDER BY t.datetime DESC";
      }
    }
    
    // Execute query
    $sth = $pdo->prepare($sql);
    $sth->execute();
    
    // Fetch results
    $tasks = array();
    while($row = $sth->fetch(PDO::FETCH_ASSOC)){
      // Format last updated time
      $lastUpdated = null;
      if ($row['last_message_time']) {
        $lastUpdated = date('d/m/Y H:i', strtotime($row['last_message_time']));
      }
      
      // Format data for frontend
      $tasks[] = array(
        'id' => '#' . $row['id'],
        'description' => $row['name'],
        'department' => $row['department_name'] ?? 'General',
        'department_id' => $row['department_id'],
        'category' => $row['category'],
        'status' => $row['status'],
        'priority' => $row['priority'],
        'createdDate' => $row['datetime'],
        'admin_id' => $row['assigned_admin_id'],
        'assignedTo' => trim($row['assigned_first_name'] . ' ' . $row['assigned_last_name']) ?: 'Unassigned',
        'content' => $row['content'],
        'lastUpdated' => $lastUpdated
      );
    }
    
    // Return tasks as JSON
    header('Content-Type: application/json');
    echo json_encode($tasks);
    
  } catch (Exception $e) {
    error_log("Error in get_dashboard_tasks: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to fetch tasks: ' . $e->getMessage()]);
  }
}

elseif($_GET['f'] == 'add_task'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();
    
    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);
    
    // Validate required fields
    if (!isset($data['description']) || trim($data['description']) === '') {
      throw new Exception("Task description is required");
    }
    
    // Prepare data with proper null handling
    $name = trim($data['description']);
    $content = isset($data['content']) ? trim($data['content']) : '';
    $category = isset($data['category']) && !empty($data['category']) ? $data['category'] : 'Other';
    
    // Handle department_id - convert empty string to null
    $department_id = null;
    if (isset($data['department_id']) && !empty($data['department_id']) && is_numeric($data['department_id'])) {
      $department_id = (int)$data['department_id'];
    }
    
    // Handle admin_id - convert empty string to null
    $assigned_admin_id = null;
    if (isset($data['admin_id']) && !empty($data['admin_id']) && is_numeric($data['admin_id'])) {
      $assigned_admin_id = (int)$data['admin_id'];
    }
    
    // Set default priority and status
    $priority = isset($data['priority']) && !empty($data['priority']) ? $data['priority'] : 'Low';
    $status = isset($data['status']) && !empty($data['status']) ? $data['status'] : 'Pending';
    
    // Convert "Open" to "In Progress" for consistency
    if ($status === 'Open') {
      $status = 'In Progress';
    }
    
    // Insert task
    $sth = $pdo->prepare("
      INSERT INTO tasks 
      (name, content, category, admin_id, department_id, priority, datetime, status) 
      VALUES 
      (:name, :content, :category, :admin_id, :department_id, :priority, NOW(), :status)
    ");
    
    $sth->bindValue(':name', $name);
    $sth->bindValue(':content', $content);
    $sth->bindValue(':category', $category);
    $sth->bindValue(':admin_id', $assigned_admin_id, PDO::PARAM_INT);
    $sth->bindValue(':department_id', $department_id, PDO::PARAM_INT);
    $sth->bindValue(':priority', $priority);
    $sth->bindValue(':status', $status);
    
    $sth->execute();
    $task_id = $pdo->lastInsertId();
    
    // Add to activity log
    $sql = "INSERT INTO activity_log 
            (user_id, action, description, user_name, timestamp) 
            VALUES 
            (:user_id, :action, :description, :user_name, NOW())";

    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':user_id', $admin_id);
    $stmt->bindValue(':action', 'New Task Created');
    $stmt->bindValue(':description', $name);

    // Get admin name
    $admin_sth = $pdo->prepare("SELECT first_name, last_name FROM admins WHERE id = :admin_id");
    $admin_sth->bindValue(':admin_id', $admin_id);
    $admin_sth->execute();
    $admin = $admin_sth->fetch(PDO::FETCH_ASSOC);
    $admin_name = trim(($admin['first_name'] ?? '') . ' ' . ($admin['last_name'] ?? ''));

    $stmt->bindValue(':user_name', $admin_name);
    $stmt->execute();
    
    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'task_id' => $task_id,
      'message' => 'Task created successfully'
    ]);
    
  } catch (Exception $e) {
    error_log("Error in add_task: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to create task: ' . $e->getMessage()]);
  }
}

// UPDATE TASK - Fixed with better validation and null handling
elseif($_GET['f'] == 'update_task'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();
    
    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);
    
    // Validate JSON data
    if (!$data) {
      throw new Exception("Invalid JSON data received");
    }
    
    // Validate required fields
    if (!isset($data['id']) || !is_numeric(str_replace('#', '', $data['id']))) {
      throw new Exception("Valid task ID is required");
    }
    
    // Get original task ID (remove # if present)
    $task_id = (int)str_replace('#', '', $data['id']);
    
    // Check if task exists and get its department
    $check_sth = $pdo->prepare("SELECT * FROM tasks WHERE id = :id");
    $check_sth->bindValue(':id', $task_id, PDO::PARAM_INT);
    $check_sth->execute();

    if ($check_sth->rowCount() == 0) {
      throw new Exception("Task not found");
    }

    $existing_task = $check_sth->fetch(PDO::FETCH_ASSOC);

    // Check if admin has access to this task's department
    $tablesResult = $pdo->query("SHOW TABLES");
    $tables = $tablesResult->fetchAll(PDO::FETCH_COLUMN);
    $adminDeptTableExists = in_array('admin_departments', $tables);

    // Get admin's primary department
    $adminDeptQuery = $pdo->prepare("SELECT department_id FROM admins WHERE id = :admin_id");
    $adminDeptQuery->bindValue(':admin_id', $admin_id);
    $adminDeptQuery->execute();
    $adminDept = $adminDeptQuery->fetch(PDO::FETCH_ASSOC);
    $primaryDeptId = $adminDept ? $adminDept['department_id'] : null;

    // Get admin's assigned departments if the table exists
    $assignedDeptIds = [];
    if ($adminDeptTableExists) {
      $assignedDeptsQuery = $pdo->prepare("SELECT department_id FROM admin_departments WHERE admin_id = :admin_id");
      $assignedDeptsQuery->bindValue(':admin_id', $admin_id);
      $assignedDeptsQuery->execute();
      while ($dept = $assignedDeptsQuery->fetch(PDO::FETCH_ASSOC)) {
        $assignedDeptIds[] = $dept['department_id'];
      }
    }

    // If primary department exists, add it to assigned departments
    if ($primaryDeptId && !in_array($primaryDeptId, $assignedDeptIds)) {
      $assignedDeptIds[] = $primaryDeptId;
    }

    // Check if admin can access this task
    $canAccess = false;
    if (empty($assignedDeptIds)) {
      // Admin has no assigned departments, can only access tasks with no department
      $canAccess = ($existing_task['department_id'] === null);
    } else {
      // Admin can access tasks from assigned departments or tasks with no department
      $canAccess = ($existing_task['department_id'] === null || in_array($existing_task['department_id'], $assignedDeptIds));
    }

    if (!$canAccess) {
      throw new Exception("Access denied: You don't have permission to modify this task");
    }
    
    // Prepare update fields
    $updates = [];
    $params = [];
    
    // Only update provided fields with proper validation
    if (isset($data['description']) && !empty(trim($data['description']))) {
      $updates[] = "name = :name";
      $params[':name'] = trim($data['description']);
    }
    
    if (isset($data['content'])) {
      $updates[] = "content = :content";
      $params[':content'] = trim($data['content']);
    }
    
    if (isset($data['category']) && !empty($data['category'])) {
      $updates[] = "category = :category";
      $params[':category'] = $data['category'];
    }
    
    // Handle department_id - convert empty string to null
    if (isset($data['department_id'])) {
      $updates[] = "department_id = :department_id";
      if (empty($data['department_id']) || $data['department_id'] === '' || $data['department_id'] === '0') {
        $params[':department_id'] = null;
      } else {
        $params[':department_id'] = (int)$data['department_id'];
      }
    }
    
    // Handle admin_id - convert empty string to null
    if (isset($data['admin_id'])) {
      $updates[] = "admin_id = :admin_id";
      if (empty($data['admin_id']) || $data['admin_id'] === '' || $data['admin_id'] === '0') {
        $params[':admin_id'] = null;
      } else {
        $params[':admin_id'] = (int)$data['admin_id'];
      }
    }
    
    if (isset($data['priority']) && !empty($data['priority'])) {
      $updates[] = "priority = :priority";
      $params[':priority'] = $data['priority'];
    }
    
    if (isset($data['status']) && !empty($data['status'])) {
      // Convert "Open" to "In Progress" for consistency
      $status = $data['status'] === 'Open' ? 'In Progress' : $data['status'];
      $updates[] = "status = :status";
      $params[':status'] = $status;
    }
    
    // Only proceed if there are fields to update
    if (empty($updates)) {
      throw new Exception("No valid fields to update");
    }
    
    // Build update query
    $sql = "UPDATE tasks SET " . implode(", ", $updates) . " WHERE id = :id";
    $params[':id'] = $task_id;
    
    // Execute update
    $sth = $pdo->prepare($sql);
    
    // Bind parameters with proper types
    foreach ($params as $key => $value) {
      if ($key === ':id' || $key === ':department_id' || $key === ':admin_id') {
        $sth->bindValue($key, $value, PDO::PARAM_INT);
      } else {
        $sth->bindValue($key, $value, PDO::PARAM_STR);
      }
    }
    
    $sth->execute();
    
    // Add to activity log
    $sql = "INSERT INTO activity_log 
            (user_id, action, description, user_name, timestamp) 
            VALUES 
            (:user_id, :action, :description, :user_name, NOW())";

    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':user_id', $admin_id);
    $stmt->bindValue(':action', 'Task Updated');
    $stmt->bindValue(':description', "Task #$task_id updated");

    // Get admin name
    $admin_sth = $pdo->prepare("SELECT first_name, last_name FROM admins WHERE id = :admin_id");
    $admin_sth->bindValue(':admin_id', $admin_id);
    $admin_sth->execute();
    $admin = $admin_sth->fetch(PDO::FETCH_ASSOC);
    $admin_name = trim(($admin['first_name'] ?? '') . ' ' . ($admin['last_name'] ?? ''));

    $stmt->bindValue(':user_name', $admin_name);
    $stmt->execute();
    
    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Task updated successfully'
    ]);
    
  } catch (Exception $e) {
    error_log("Error in update_task: " . $e->getMessage());
    error_log("Update task data: " . print_r($data ?? 'No data', true));
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to update task: ' . $e->getMessage()]);
  }
}

// DELETE TASK
elseif($_GET['f'] == 'delete_task'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();
    
    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);
    
    // Validate required fields
    if (!isset($data['id']) || !is_numeric(str_replace('#', '', $data['id']))) {
      throw new Exception("Valid task ID is required");
    }
    
    // Get original task ID (remove # if present)
    $task_id = (int)str_replace('#', '', $data['id']);
    
    // Check if task exists and get its department
    $check_sth = $pdo->prepare("SELECT name, department_id FROM tasks WHERE id = :id");
    $check_sth->bindValue(':id', $task_id);
    $check_sth->execute();

    if ($check_sth->rowCount() == 0) {
      throw new Exception("Task not found");
    }

    $task = $check_sth->fetch(PDO::FETCH_ASSOC);
    $task_name = $task['name'];

    // Check if admin has access to this task's department
    $tablesResult = $pdo->query("SHOW TABLES");
    $tables = $tablesResult->fetchAll(PDO::FETCH_COLUMN);
    $adminDeptTableExists = in_array('admin_departments', $tables);

    // Get admin's primary department
    $adminDeptQuery = $pdo->prepare("SELECT department_id FROM admins WHERE id = :admin_id");
    $adminDeptQuery->bindValue(':admin_id', $admin_id);
    $adminDeptQuery->execute();
    $adminDept = $adminDeptQuery->fetch(PDO::FETCH_ASSOC);
    $primaryDeptId = $adminDept ? $adminDept['department_id'] : null;

    // Get admin's assigned departments if the table exists
    $assignedDeptIds = [];
    if ($adminDeptTableExists) {
      $assignedDeptsQuery = $pdo->prepare("SELECT department_id FROM admin_departments WHERE admin_id = :admin_id");
      $assignedDeptsQuery->bindValue(':admin_id', $admin_id);
      $assignedDeptsQuery->execute();
      while ($dept = $assignedDeptsQuery->fetch(PDO::FETCH_ASSOC)) {
        $assignedDeptIds[] = $dept['department_id'];
      }
    }

    // If primary department exists, add it to assigned departments
    if ($primaryDeptId && !in_array($primaryDeptId, $assignedDeptIds)) {
      $assignedDeptIds[] = $primaryDeptId;
    }

    // Check if admin can access this task
    $canAccess = false;
    if (empty($assignedDeptIds)) {
      // Admin has no assigned departments, can only access tasks with no department
      $canAccess = ($task['department_id'] === null);
    } else {
      // Admin can access tasks from assigned departments or tasks with no department
      $canAccess = ($task['department_id'] === null || in_array($task['department_id'], $assignedDeptIds));
    }

    if (!$canAccess) {
      throw new Exception("Access denied: You don't have permission to delete this task");
    }
    
    // Delete related messages first
    $del_messages = $pdo->prepare("DELETE FROM task_messages WHERE task_id = :task_id");
    $del_messages->bindValue(':task_id', $task_id);
    $del_messages->execute();
    
    // Delete task
    $sth = $pdo->prepare("DELETE FROM tasks WHERE id = :id");
    $sth->bindValue(':id', $task_id);
    $sth->execute();
    
    // Add to activity log
    $sql = "INSERT INTO activity_log 
            (user_id, action, description, user_name, timestamp) 
            VALUES 
            (:user_id, :action, :description, :user_name, NOW())";

    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':user_id', $admin_id);
    $stmt->bindValue(':action', 'Task Deleted');
    $stmt->bindValue(':description', "Deleted: $task_name (#$task_id)");

    // Get admin name
    $admin_sth = $pdo->prepare("SELECT first_name, last_name FROM admins WHERE id = :admin_id");
    $admin_sth->bindValue(':admin_id', $admin_id);
    $admin_sth->execute();
    $admin = $admin_sth->fetch(PDO::FETCH_ASSOC);
    $admin_name = trim(($admin['first_name'] ?? '') . ' ' . ($admin['last_name'] ?? ''));

    $stmt->bindValue(':user_name', $admin_name);
    $stmt->execute();
    
    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Task deleted successfully'
    ]);
    
  } catch (Exception $e) {
    error_log("Error in delete_task: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to delete task: ' . $e->getMessage()]);
  }
}
  
  // GET RECENT ORDERS
  elseif($_GET['f'] == 'get_recent_orders'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();
      
      // Get the most recent orders
      $sql = "SELECT 
              o.id, 
              o.label, 
              o.order_type, 
              o.status, 
              o.recurring_price as amount,
              o.order_date as date,
              u.first_name, 
              u.last_name,
              u.company_name as customerName
              FROM orders o
              LEFT JOIN users u ON o.owner_id = u.id
              ORDER BY o.order_date DESC
              LIMIT 5";
      
      $sth = $pdo->prepare($sql);
      $sth->execute();
      
      $orders = array();
      while($row = $sth->fetch(PDO::FETCH_ASSOC)){
        $orders[] = array(
          'id' => $row['id'],
          'label' => $row['label'],
          'customerName' => !empty($row['company_name']) ? $row['company_name'] : trim($row['first_name'] . ' ' . $row['last_name']),
          'amount' => '€' . number_format($row['amount'], 2),
          'date' => $row['date'],
          'status' => $row['status'],
          'type' => $row['order_type']
        );
      }
      
      // Return orders as JSON
      header('Content-Type: application/json');
      echo json_encode($orders);
      
    } catch (Exception $e) {
      error_log("Error in get_recent_orders: " . $e->getMessage());
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode(['error' => 'Failed to fetch recent orders: ' . $e->getMessage()]);
    }
  }
  
  // Create an activity_log table if it doesn't exist
  // This would normally be part of the database schema, but adding here for completeness
  elseif($_GET['f'] == 'init_activity_log'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();
      
      // Create activity_log table if it doesn't exist
      $sql = "CREATE TABLE IF NOT EXISTS `activity_log` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `action` varchar(64) NOT NULL,
        `description` text NOT NULL,
        `user_name` varchar(128) NOT NULL,
        `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_timestamp` (`timestamp`),
        KEY `idx_user_id` (`user_id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
      
      $pdo->exec($sql);
      
      echo json_encode(['success' => true, 'message' => 'Activity log table initialized']);
      
    } catch (Exception $e) {
      echo json_encode(['error' => 'Failed to initialize activity log: ' . $e->getMessage()]);
    }
  }
  
  // GET RECENT ACTIVITY
  elseif($_GET['f'] == 'get_recent_activity'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();
      
      // Check if activity_log table exists, create it if not
      $tableCheck = $pdo->query("SHOW TABLES LIKE 'activity_log'");
      if ($tableCheck->rowCount() == 0) {
        // Create activity_log table
        $sql = "CREATE TABLE IF NOT EXISTS `activity_log` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `user_id` int(11) NOT NULL,
          `action` varchar(64) NOT NULL,
          `description` text NOT NULL,
          `user_name` varchar(128) NOT NULL,
          `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `idx_timestamp` (`timestamp`),
          KEY `idx_user_id` (`user_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        
        $pdo->exec($sql);
        
        // Add sample initial activity if no activities exist
        $initialActivity = $pdo->prepare("INSERT INTO activity_log 
                                         (user_id, action, description, user_name, timestamp) 
                                         VALUES 
                                         (:user_id, 'System Initialized', 'Activity tracking started', 'System', NOW())");
        $initialActivity->bindValue(':user_id', $admin_id);
        $initialActivity->execute();
      }
      
      // Get filter parameter (today, yesterday, this week, etc.)
      $timeFilter = isset($_GET['timeFilter']) ? $_GET['timeFilter'] : 'today';
      
      // Build time filter condition
      $timeCondition = "1=1"; // Default: all time
      switch($timeFilter) {
        case 'today':
          $timeCondition = "DATE(timestamp) = CURDATE()";
          break;
        case 'yesterday':
          $timeCondition = "DATE(timestamp) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)";
          break;
        case 'thisWeek':
          $timeCondition = "YEARWEEK(timestamp) = YEARWEEK(NOW())";
          break;
        case 'thisMonth':
          $timeCondition = "MONTH(timestamp) = MONTH(NOW()) AND YEAR(timestamp) = YEAR(NOW())";
          break;
      }
      
      // Get recent activity
      $sql = "SELECT * FROM activity_log 
              WHERE $timeCondition
              ORDER BY timestamp DESC
              LIMIT 10";
      
      $sth = $pdo->prepare($sql);
      $sth->execute();
      
      $activities = array();
      while($row = $sth->fetch(PDO::FETCH_ASSOC)){
        $activities[] = array(
          'id' => $row['id'],
          'action' => $row['action'],
          'description' => $row['description'],
          'user' => $row['user_name'],
          'timestamp' => date('H:i', strtotime($row['timestamp']))
        );
      }
      
      // Return activities as JSON
      header('Content-Type: application/json');
      echo json_encode($activities);
      
    } catch (Exception $e) {
      error_log("Error in get_recent_activity: " . $e->getMessage());
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode(['error' => 'Failed to fetch recent activity: ' . $e->getMessage()]);
    }
  }
  
  // GET TASK STATS - Updated to treat "Open" as "In Progress"
  elseif($_GET['f'] == 'get_task_stats'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();

      // Check if admin_departments table exists
      $tablesResult = $pdo->query("SHOW TABLES");
      $tables = $tablesResult->fetchAll(PDO::FETCH_COLUMN);
      $adminDeptTableExists = in_array('admin_departments', $tables);

      // Get admin's primary department
      $adminDeptQuery = $pdo->prepare("SELECT department_id FROM admins WHERE id = :admin_id");
      $adminDeptQuery->bindValue(':admin_id', $admin_id);
      $adminDeptQuery->execute();
      $adminDept = $adminDeptQuery->fetch(PDO::FETCH_ASSOC);
      $primaryDeptId = $adminDept ? $adminDept['department_id'] : null;

      // Get admin's assigned departments if the table exists
      $assignedDeptIds = [];
      if ($adminDeptTableExists) {
        $assignedDeptsQuery = $pdo->prepare("SELECT department_id FROM admin_departments WHERE admin_id = :admin_id");
        $assignedDeptsQuery->bindValue(':admin_id', $admin_id);
        $assignedDeptsQuery->execute();
        while ($dept = $assignedDeptsQuery->fetch(PDO::FETCH_ASSOC)) {
          $assignedDeptIds[] = $dept['department_id'];
        }
      }

      // If primary department exists, add it to assigned departments
      if ($primaryDeptId && !in_array($primaryDeptId, $assignedDeptIds)) {
        $assignedDeptIds[] = $primaryDeptId;
      }

      // Build the WHERE clause for department filtering
      $whereClause = "WHERE 1=1";
      if (!empty($assignedDeptIds)) {
        $departmentConditions = [];
        foreach ($assignedDeptIds as $deptId) {
          $departmentConditions[] = "department_id = " . intval($deptId);
        }
        // Also include tasks with no department assigned (NULL department_id)
        $departmentConditions[] = "department_id IS NULL";
        $whereClause .= " AND (" . implode(" OR ", $departmentConditions) . ")";
      } else {
        // If admin has no assigned departments, only show tasks with no department
        $whereClause .= " AND department_id IS NULL";
      }

      // Get counts of tasks by status with department filtering
      $sql = "SELECT
              COUNT(*) as total,
              SUM(CASE WHEN status IN ('In Progress', 'Open') THEN 1 ELSE 0 END) as inProgress,
              SUM(CASE WHEN status = 'Pending' OR status = 'Pending Approval' THEN 1 ELSE 0 END) as pending,
              SUM(CASE WHEN status IN ('Completed', 'Closed', 'Resolved') THEN 1 ELSE 0 END) as completed
              FROM tasks $whereClause";

      $sth = $pdo->prepare($sql);
      $sth->execute();
      $stats = $sth->fetch(PDO::FETCH_ASSOC);

      // Convert null values to 0
      $stats['total'] = $stats['total'] ?? 0;
      $stats['inProgress'] = $stats['inProgress'] ?? 0;
      $stats['pending'] = $stats['pending'] ?? 0;
      $stats['completed'] = $stats['completed'] ?? 0;

      // Return stats as JSON
      header('Content-Type: application/json');
      echo json_encode($stats);

    } catch (Exception $e) {
      error_log("Error in get_task_stats: " . $e->getMessage());
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode(['error' => 'Failed to fetch task stats: ' . $e->getMessage()]);
    }
  }
  
  
  
  
  elseif($_GET['f'] == 'get_task_messages'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();
      
      // Get request data
      $data = json_decode(file_get_contents('php://input'), true);
      
      // Validate required fields
      if (!isset($data['task_id']) || empty($data['task_id'])) {
        throw new Exception("Task ID is required");
      }
      
      // Remove # from task_id if present
      $task_id = (int)str_replace('#', '', $data['task_id']);

      // Check if task exists and if admin has access to it
      $check_sth = $pdo->prepare("SELECT department_id FROM tasks WHERE id = :id");
      $check_sth->bindValue(':id', $task_id);
      $check_sth->execute();

      if ($check_sth->rowCount() == 0) {
        throw new Exception("Task not found");
      }

      $task = $check_sth->fetch(PDO::FETCH_ASSOC);

      // Check if admin has access to this task's department
      $tablesResult = $pdo->query("SHOW TABLES");
      $tables = $tablesResult->fetchAll(PDO::FETCH_COLUMN);
      $adminDeptTableExists = in_array('admin_departments', $tables);

      // Get admin's primary department
      $adminDeptQuery = $pdo->prepare("SELECT department_id FROM admins WHERE id = :admin_id");
      $adminDeptQuery->bindValue(':admin_id', $admin_id);
      $adminDeptQuery->execute();
      $adminDept = $adminDeptQuery->fetch(PDO::FETCH_ASSOC);
      $primaryDeptId = $adminDept ? $adminDept['department_id'] : null;

      // Get admin's assigned departments if the table exists
      $assignedDeptIds = [];
      if ($adminDeptTableExists) {
        $assignedDeptsQuery = $pdo->prepare("SELECT department_id FROM admin_departments WHERE admin_id = :admin_id");
        $assignedDeptsQuery->bindValue(':admin_id', $admin_id);
        $assignedDeptsQuery->execute();
        while ($dept = $assignedDeptsQuery->fetch(PDO::FETCH_ASSOC)) {
          $assignedDeptIds[] = $dept['department_id'];
        }
      }

      // If primary department exists, add it to assigned departments
      if ($primaryDeptId && !in_array($primaryDeptId, $assignedDeptIds)) {
        $assignedDeptIds[] = $primaryDeptId;
      }

      // Check if admin can access this task
      $canAccess = false;
      if (empty($assignedDeptIds)) {
        // Admin has no assigned departments, can only access tasks with no department
        $canAccess = ($task['department_id'] === null);
      } else {
        // Admin can access tasks from assigned departments or tasks with no department
        $canAccess = ($task['department_id'] === null || in_array($task['department_id'], $assignedDeptIds));
      }

      if (!$canAccess) {
        throw new Exception("Access denied: You don't have permission to view messages for this task");
      }

      // Create task_messages table if it doesn't exist
      $sql = "CREATE TABLE IF NOT EXISTS `task_messages` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `task_id` int(11) NOT NULL,
        `admin_id` int(11) NOT NULL,
        `message` text NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `task_id` (`task_id`),
        KEY `idx_created_at` (`created_at`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
      
      $pdo->exec($sql);
      
      // Get messages for the task
      $sth = $pdo->prepare("
        SELECT m.*, a.first_name, a.last_name
        FROM task_messages m
        LEFT JOIN admins a ON m.admin_id = a.id
        WHERE m.task_id = :task_id
        ORDER BY m.created_at ASC
      ");
      
      $sth->bindValue(':task_id', $task_id);
      $sth->execute();
      
      $messages = [];
      while ($row = $sth->fetch(PDO::FETCH_ASSOC)) {
        $messages[] = [
          'id' => $row['id'],
          'message' => $row['message'],
          'admin_name' => trim($row['first_name'] . ' ' . $row['last_name']),
          'created_at' => $row['created_at'],
          'time' => date('H:i', strtotime($row['created_at'])),
          'date' => date('d M Y', strtotime($row['created_at']))
        ];
      }
      
      // Return messages as JSON
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'messages' => $messages
      ]);
      
    } catch (Exception $e) {
      error_log("Error in get_task_messages: " . $e->getMessage());
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode(['error' => 'Failed to fetch task messages: ' . $e->getMessage()]);
    }
  }
  
  elseif($_GET['f'] == 'add_task_message'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();
      
      // Get request data
      $data = json_decode(file_get_contents('php://input'), true);
      
      // Validate required fields
      if (!isset($data['task_id']) || empty($data['task_id'])) {
        throw new Exception("Task ID is required");
      }
      
      if (!isset($data['message']) || trim($data['message']) === '') {
        throw new Exception("Message is required");
      }
      
      // Remove # from task_id if present
      $task_id = (int)str_replace('#', '', $data['task_id']);
      $message = trim($data['message']);

      // Check if task exists and if admin has access to it
      $check_sth = $pdo->prepare("SELECT department_id FROM tasks WHERE id = :id");
      $check_sth->bindValue(':id', $task_id);
      $check_sth->execute();

      if ($check_sth->rowCount() == 0) {
        throw new Exception("Task not found");
      }

      $task = $check_sth->fetch(PDO::FETCH_ASSOC);

      // Check if admin has access to this task's department
      $tablesResult = $pdo->query("SHOW TABLES");
      $tables = $tablesResult->fetchAll(PDO::FETCH_COLUMN);
      $adminDeptTableExists = in_array('admin_departments', $tables);

      // Get admin's primary department
      $adminDeptQuery = $pdo->prepare("SELECT department_id FROM admins WHERE id = :admin_id");
      $adminDeptQuery->bindValue(':admin_id', $admin_id);
      $adminDeptQuery->execute();
      $adminDept = $adminDeptQuery->fetch(PDO::FETCH_ASSOC);
      $primaryDeptId = $adminDept ? $adminDept['department_id'] : null;

      // Get admin's assigned departments if the table exists
      $assignedDeptIds = [];
      if ($adminDeptTableExists) {
        $assignedDeptsQuery = $pdo->prepare("SELECT department_id FROM admin_departments WHERE admin_id = :admin_id");
        $assignedDeptsQuery->bindValue(':admin_id', $admin_id);
        $assignedDeptsQuery->execute();
        while ($dept = $assignedDeptsQuery->fetch(PDO::FETCH_ASSOC)) {
          $assignedDeptIds[] = $dept['department_id'];
        }
      }

      // If primary department exists, add it to assigned departments
      if ($primaryDeptId && !in_array($primaryDeptId, $assignedDeptIds)) {
        $assignedDeptIds[] = $primaryDeptId;
      }

      // Check if admin can access this task
      $canAccess = false;
      if (empty($assignedDeptIds)) {
        // Admin has no assigned departments, can only access tasks with no department
        $canAccess = ($task['department_id'] === null);
      } else {
        // Admin can access tasks from assigned departments or tasks with no department
        $canAccess = ($task['department_id'] === null || in_array($task['department_id'], $assignedDeptIds));
      }

      if (!$canAccess) {
        throw new Exception("Access denied: You don't have permission to add messages to this task");
      }

      // Insert the message
      $sth = $pdo->prepare("
        INSERT INTO task_messages 
        (task_id, admin_id, message)
        VALUES
        (:task_id, :admin_id, :message)
      ");
      
      $sth->bindValue(':task_id', $task_id);
      $sth->bindValue(':admin_id', $admin_id);
      $sth->bindValue(':message', $message);
      $sth->execute();
      
      $message_id = $pdo->lastInsertId();
      
      // Get the admin name for the response
      $admin_sth = $pdo->prepare("SELECT first_name, last_name FROM admins WHERE id = :admin_id");
      $admin_sth->bindValue(':admin_id', $admin_id);
      $admin_sth->execute();
      $admin = $admin_sth->fetch(PDO::FETCH_ASSOC);
      $admin_name = trim($admin['first_name'] . ' ' . $admin['last_name']);
      
      // Log activity
      $sql = "INSERT INTO activity_log 
              (user_id, action, description, user_name, timestamp) 
              VALUES 
              (:user_id, :action, :description, :user_name, NOW())";
  
      $stmt = $pdo->prepare($sql);
      $stmt->bindValue(':user_id', $admin_id);
      $stmt->bindValue(':action', 'Task Message Added');
      $stmt->bindValue(':description', "Added message to task #$task_id");
      $stmt->bindValue(':user_name', $admin_name);
      $stmt->execute();
      
      // Return success response
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'message_id' => $message_id,
        'admin_name' => $admin_name,
        'created_at' => date('Y-m-d H:i:s'),
        'time' => date('H:i'),
        'date' => date('d M Y')
      ]);
      
    } catch (Exception $e) {
      error_log("Error in add_task_message: " . $e->getMessage());
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode(['error' => 'Failed to add task message: ' . $e->getMessage()]);
    }
  }



// GET STAFF MEMBERS - Database-driven with fallback
elseif($_GET['f'] == 'get_staff_members'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();
    
    // Query the actual admins table
    $sql = "SELECT id, first_name, last_name FROM admins ORDER BY first_name, last_name";
    $sth = $pdo->prepare($sql);
    $sth->execute();
    
    if($sth->rowCount() > 0) {
      $staff = array();
      while($row = $sth->fetch(PDO::FETCH_ASSOC)){
        $staff[] = array(
          'id' => $row['id'],
          'name' => trim($row['first_name'] . ' ' . $row['last_name']),
          'first_name' => $row['first_name']
        );
      }
      
      // Return staff as JSON
      header('Content-Type: application/json');
      echo json_encode($staff);
    } else {
      // No data found, return hardcoded fallback
      throw new Exception("No staff found in database");
    }
    
  } catch (Exception $e) {
    error_log("Error in get_staff_members: " . $e->getMessage());
    
    // Return hardcoded fallback only if database query fails
    // This ensures we always return something usable
    header('Content-Type: application/json');
    echo json_encode([
      array('id' => 1, 'name' => 'Alexandru', 'first_name' => 'Alexandru'),
      array('id' => 2, 'name' => 'Mario', 'first_name' => 'Mario'),
      array('id' => 3, 'name' => 'Raul', 'first_name' => 'Raul'),
      array('id' => 4, 'name' => 'Sorin', 'first_name' => 'Sorin'),
      array('id' => 5, 'name' => 'Mihai', 'first_name' => 'Mihai'),
      array('id' => 6, 'name' => 'Andrei', 'first_name' => 'Andrei'),
      array('id' => 7, 'name' => 'Michael', 'first_name' => 'Michael')
    ]);
  }
}

// GET DEPARTMENTS - Database-driven with fallback
elseif($_GET['f'] == 'get_departments'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();
    
    // Query the actual departments table
    $sql = "SELECT id, department_name FROM departments ORDER BY department_name";
    $sth = $pdo->prepare($sql);
    $sth->execute();
    
    if($sth->rowCount() > 0) {
      $departments = array();
      while($row = $sth->fetch(PDO::FETCH_ASSOC)){
        $departments[] = array(
          'id' => $row['id'],
          'name' => $row['department_name']
        );
      }
      
      // Return departments as JSON
      header('Content-Type: application/json');
      echo json_encode($departments);
    } else {
      // No data found, return empty array
      header('Content-Type: application/json');
      echo json_encode([]);
    }
    
  } catch (Exception $e) {
    error_log("Error in get_departments: " . $e->getMessage());
    // Return empty array on error
    header('Content-Type: application/json');
    echo json_encode([]);
  }
}

// MIGRATE OPEN STATUS - Helper endpoint to manually trigger migration if needed
elseif($_GET['f'] == 'migrate_open_status'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();
    
    // Count tasks with Open status before migration
    $count_sth = $pdo->prepare("SELECT COUNT(*) as count FROM tasks WHERE status = 'Open'");
    $count_sth->execute();
    $before_count = $count_sth->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Perform migration
    $pdo->exec("UPDATE tasks SET status = 'In Progress' WHERE status = 'Open'");
    
    // Log the migration
    $sql = "INSERT INTO activity_log 
            (user_id, action, description, user_name, timestamp) 
            VALUES 
            (:user_id, 'Status Migration', :description, 'System', NOW())";

    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':user_id', $admin_id);
    $stmt->bindValue(':description', "Migrated $before_count tasks from 'Open' to 'In Progress' status");
    $stmt->execute();
    
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => "Successfully migrated $before_count tasks from 'Open' to 'In Progress' status"
    ]);
    
  } catch (Exception $e) {
    error_log("Error in migrate_open_status: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to migrate status: ' . $e->getMessage()]);
  }
}

?>