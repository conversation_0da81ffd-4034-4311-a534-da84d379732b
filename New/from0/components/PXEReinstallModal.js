import React, { useState, useEffect } from 'react';
import {
  X,
  RefreshCw,
  Server,
  AlertTriangle,
  CheckCircle,
  Terminal,
  Clock
} from 'lucide-react';
import { API_URL } from '../config';

const PXEReinstallModal = ({ 
  isOpen, 
  onClose, 
  server, 
  serverType, 
  onReinstallComplete,
  onStatusChange
}) => {
  const [operatingSystems, setOperatingSystems] = useState([]);
  const [selectedOS, setSelectedOS] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isInstalling, setIsInstalling] = useState(false);
  const [installProgress, setInstallProgress] = useState('');
  const [customRootPassword, setCustomRootPassword] = useState('');
  const [useCustomPassword, setUseCustomPassword] = useState(false);
  const [installationInProgress, setInstallationInProgress] = useState(false);
  const [installationStatus, setInstallationStatus] = useState(null);
  const [checkingStatus, setCheckingStatus] = useState(false);

  // Debug state changes
  useEffect(() => {
    console.log('🔄 installationInProgress changed:', installationInProgress);
    if (installationInProgress && installationStatus) {
      console.log('📊 Installation status details:', installationStatus);
    }
  }, [installationInProgress, installationStatus]);

  // Fetch available operating systems and check installation status
  useEffect(() => {
    if (isOpen) {
      console.log('🔄 Modal opened, fetching OS list and checking installation status...');
      fetchOperatingSystems();
      checkInstallationStatus();
      
      // Set up periodic status check every 10 seconds
      const interval = setInterval(() => {
        if (!isInstalling) {
          console.log('⏰ Periodic status check...');
          checkInstallationStatus();
        }
      }, 10000);
      
      return () => {
        console.log('🧹 Cleaning up periodic status check');
        clearInterval(interval);
      };
    }
  }, [isOpen, isInstalling]);

  const checkInstallationStatus = async () => {
    try {
      setCheckingStatus(true);
      const token = localStorage.getItem('admin_token');
      
      console.log('🔍 Checking installation status for server:', server.id);
      
      const response = await fetch(`${API_URL}/pxe_api_integration.php?f=check_installation_status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          server_id: server.id
        })
      });

      console.log('📡 Status check response HTTP code:', response.status);

      if (!response.ok) {
        console.warn('⚠️ Installation status check failed with HTTP error:', response.status);
        const errorText = await response.text();
        console.warn('Error response body:', errorText);
        // If the endpoint doesn't exist or fails, assume no installation in progress
        setInstallationInProgress(false);
        if (onStatusChange) onStatusChange(false);
        setInstallationStatus(null);
        return false;
      }

      const data = await response.json();
      console.log('📦 Installation status response data:', data);
      
      if (data.installation_in_progress) {
        console.log('🚫 Installation in progress detected!');
        setInstallationInProgress(true);
        if (onStatusChange) onStatusChange(true);
        setInstallationStatus(data);
        return true;
      } else {
        console.log('✅ No installation in progress');
        setInstallationInProgress(false);
        if (onStatusChange) onStatusChange(false);
        setInstallationStatus(null);
        return false;
      }
    } catch (err) {
      console.error('❌ Error checking installation status:', err);
      // If there's an error, assume no installation in progress for safety
      setInstallationInProgress(false);
      if (onStatusChange) onStatusChange(false);
      setInstallationStatus(null);
      return false;
    } finally {
      setCheckingStatus(false);
    }
  };

  const fetchOperatingSystems = async () => {
    try {
      setLoading(true);
      setError('');

      const token = localStorage.getItem('admin_token');
      const response = await fetch(`${API_URL}/pxe_api_integration.php?f=reinstallable_os`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();
      
      if (Array.isArray(data)) {
        setOperatingSystems(data);
        if (data.length > 0) {
          setSelectedOS(data[0].id.toString());
        }
      } else {
        throw new Error('Invalid response format');
      }

    } catch (err) {
      console.error('Error fetching operating systems:', err);
      setError(`Failed to load operating systems: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleStartReinstall = async () => {
    try {
      if (!selectedOS) {
        setError('Please select an operating system');
        return;
      }

      setError('');
      
      // Double-check installation status before proceeding
      console.log('🔄 Double-checking installation status before starting new installation...');
      const isInstallationRunning = await checkInstallationStatus();
      
      if (isInstallationRunning) {
        const errorMsg = 'Cannot start installation: Another installation is already in progress for this server';
        setError(errorMsg);
        console.log('🚫 Installation blocked - another installation is in progress');
        console.log('Current installation status:', installationStatus);
        return;
      }

      console.log('✅ No installation in progress, proceeding with new installation...');
      
      setLoading(true);
      setIsInstalling(true);
      setInstallProgress('Initializing PXE reinstall...');

      const token = localStorage.getItem('admin_token');
      
      // Get the selected OS template name
      const selectedOSData = operatingSystems.find(os => os.id.toString() === selectedOS);
      const osTemplate = selectedOSData?.name || 'unknown';
      
      const requestData = {
        token: token,
        server_id: server.id,
        server_type: serverType,
        os_id: selectedOS,
        os_template: osTemplate,
        ipmi_address: server.ipmi,
        ipmi_username: 'root',
        ipmi_password: server.ipmi_root_pass,
        // Use server's existing network configuration if available
        network_config: {
          ip_address: server.main_ip ? server.main_ip.split('/')[0] : server.ip || '************',
          subnet_mask: '***************',
          gateway: '************',
          hostname: server.label || `server-${server.id}`,
          dns_primary: '*******',
          dns_secondary: '*******'
        },
        mac_address: server.mac
      };

      // Add custom password if specified
      if (useCustomPassword && customRootPassword.trim()) {
        requestData.custom_root_password = customRootPassword;
      }

      console.log('🚀 Starting PXE reinstall with configuration:', requestData);

      // First, try to reserve the installation slot to prevent race conditions
      console.log('🔒 Attempting to reserve installation slot...');
      const reserveResponse = await fetch(`${API_URL}/pxe_api_integration.php?f=reserve_installation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          server_id: server.id,
          os_name: selectedOSData?.name || 'Unknown OS'
        })
      });

      console.log('📡 Reservation response HTTP code:', reserveResponse.status);

      if (!reserveResponse.ok) {
        const errorText = await reserveResponse.text();
        console.error('❌ Reservation failed with HTTP error:', reserveResponse.status, errorText);
        throw new Error(`Failed to reserve installation slot: HTTP ${reserveResponse.status}`);
      }

      const reserveResult = await reserveResponse.json();
      console.log('📦 Reservation result:', reserveResult);
      
      if (!reserveResult.success) {
        if (reserveResult.error && reserveResult.error.includes('already in progress')) {
          console.log('🚫 Reservation blocked - installation already in progress');
          if (onStatusChange) onStatusChange(true);
          throw new Error('Another installation is already in progress for this server');
        }
        console.error('❌ Reservation failed:', reserveResult.error);
        throw new Error(reserveResult.error || 'Failed to reserve installation slot');
      }

      console.log('✅ Installation slot reserved, proceeding with PXE reinstall...');

      const response = await fetch(`${API_URL}/pxe_api_integration.php?action=execute_server_reinstall`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      console.log('📡 Installation execution response HTTP code:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Installation execution failed with HTTP error:', response.status, errorText);
        throw new Error(`HTTP error ${response.status}: ${errorText}`);
      }

      const result = await response.json();
      console.log('📦 Installation execution result:', result);

      if (result.success) {
        console.log('✅ PXE reinstall initiated successfully!');
        setInstallProgress('PXE reinstall initiated successfully! The server will now boot from network and begin OS installation.');
        
        // Show success for a moment, then close
        setTimeout(() => {
          if (onReinstallComplete) {
            onReinstallComplete();
          }
          handleClose();
        }, 3000);
      } else {
        console.error('❌ PXE reinstall failed:', result.error);
        throw new Error(result.error || 'PXE reinstall failed');
      }

    } catch (err) {
      console.error('❌ Error starting PXE reinstall:', err);
      setError(`Failed to start reinstall: ${err.message}`);
      setIsInstalling(false);
      
      // After error, immediately check status to update UI
      setTimeout(() => {
        checkInstallationStatus();
      }, 1000);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setSelectedOS('');
    setError('');
    setInstallProgress('');
    setCustomRootPassword('');
    setUseCustomPassword(false);
    setIsInstalling(false);
    setInstallationInProgress(false);
    setInstallationStatus(null);
    setCheckingStatus(false);
    onClose();
  };

  const selectedOSName = operatingSystems.find(os => os.id.toString() === selectedOS)?.name || '';

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-b">
          <h3 className="text-lg font-bold flex items-center">
            <Terminal className="w-5 h-5 mr-2 text-indigo-700" />
            PXE Reinstall Server
          </h3>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700"
            disabled={loading && isInstalling}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4">
          {/* Server Info */}
          <div className="mb-4 p-3 bg-gray-50 rounded-md">
            <div className="flex items-center mb-2">
              <Server className="w-4 h-4 mr-2 text-indigo-700" />
              <span className="font-medium">{server?.label || `Server ${server?.id}`}</span>
            </div>
            <div className="text-sm text-gray-600">
              <div>Type: {serverType === 'dedicated' ? 'Dedicated Server' : 'Blade Server'}</div>
              <div>IPMI: {server?.ipmi || 'Not configured'}</div>
              {server?.mac && <div>MAC: {server.mac}</div>}
            </div>
          </div>


          {checkingStatus && !installationInProgress && !isInstalling ? (
            /* Checking Installation Status */
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <div className="flex items-center justify-center">
                <RefreshCw className="w-4 h-4 mr-2 animate-spin text-blue-600" />
                <span className="text-sm text-blue-800">Checking installation status...</span>
              </div>
            </div>
          ) : installationInProgress ? (
            /* Installation Already in Progress */
            <div>
              <div className="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-md">
                <div className="flex items-start">
                  <Clock className="w-4 h-4 mr-2 text-orange-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-orange-800">
                    <div className="font-medium mb-1">Installation Already in Progress</div>
                    <div>
                      Another PXE installation is currently running on this server.
                      {installationStatus?.os_name && ` Installing: ${installationStatus.os_name}`}
                      {installationStatus?.started_at && ` • Started: ${new Date(installationStatus.started_at).toLocaleString()}`}
                    </div>
                    {installationStatus?.acl_status && (
                      <div className="mt-2 text-xs">
                        <div className="font-medium">ACL Status:</div>
                        <div className="flex items-center space-x-4">
                          <span className={`flex items-center ${installationStatus.acl_status.removed ? 'text-green-600' : 'text-gray-500'}`}>
                            {installationStatus.acl_status.removed ? '✓' : '○'} ACLs Removed
                          </span>
                          <span className={`flex items-center ${installationStatus.acl_status.reapplied ? 'text-green-600' : 'text-gray-500'}`}>
                            {installationStatus.acl_status.reapplied ? '✓' : '○'} ACLs Reapplied
                          </span>
                        </div>
                        {(installationStatus.acl_status.removal_error || installationStatus.acl_status.reapply_error) && (
                          <div className="mt-1 text-red-600">
                            {installationStatus.acl_status.removal_error && (
                              <div>ACL Removal Error: {installationStatus.acl_status.removal_error}</div>
                            )}
                            {installationStatus.acl_status.reapply_error && (
                              <div>ACL Reapply Error: {installationStatus.acl_status.reapply_error}</div>
                            )}
                          </div>
                        )}
                      </div>
                    )}
                    <div className="mt-2">
                      Please wait for the current installation to complete before starting a new one.
                      You can monitor the progress through the server's IPMI console.
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Refresh Status Button */}
              <div className="mb-4 text-center">
                <button
                  type="button"
                  onClick={checkInstallationStatus}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-md text-sm hover:bg-indigo-700 flex items-center mx-auto"
                  disabled={checkingStatus}
                >
                  {checkingStatus ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Checking...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Refresh Status
                    </>
                  )}
                </button>
              </div>
            </div>
          ) : !isInstalling ? (
            <>
              {/* OS Selection */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Operating System <span className="text-red-500">*</span>
                </label>
                {loading ? (
                  <div className="flex items-center justify-center p-3">
                    <RefreshCw className="w-4 h-4 animate-spin mr-2" />
                    Loading operating systems...
                  </div>
                ) : (
                  <select
                    value={selectedOS}
                    onChange={(e) => setSelectedOS(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                    disabled={loading}
                  >
                    <option value="">Select an operating system</option>
                    {operatingSystems.map(os => (
                      <option key={os.id} value={os.id.toString()}>
                        {os.name}
                      </option>
                    ))}
                  </select>
                )}
              </div>

              {/* Custom Root Password Option */}
              <div className="mb-4">
                <label className="flex items-center mb-2">
                  <input
                    type="checkbox"
                    checked={useCustomPassword}
                    onChange={(e) => setUseCustomPassword(e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm font-medium text-gray-700">
                    Use custom root password
                  </span>
                </label>
                
                {useCustomPassword && (
                  <input
                    type="password"
                    value={customRootPassword}
                    onChange={(e) => setCustomRootPassword(e.target.value)}
                    placeholder="Enter custom root password"
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  />
                )}
              </div>

              {/* Warning */}
              <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <div className="flex items-start">
                  <AlertTriangle className="w-4 h-4 mr-2 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-yellow-800">
                    <div className="font-medium mb-1">Warning</div>
                    <div>
                      This will completely wipe the server and install a fresh operating system.
                      All data on the server will be permanently lost. This action cannot be undone.
                    </div>
                  </div>
                </div>
              </div>
            </>
          ) : (
            /* Installation Confirmation */
            <div>
              <div className="text-center mb-4">
                <div className="font-medium text-gray-900">Installation Started</div>
                <div className="text-sm text-gray-600 mt-1">
                  {installProgress}
                </div>
              </div>

              <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                <div className="flex items-start">
                  <CheckCircle className="w-4 h-4 mr-2 text-green-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-green-800">
                    Installation process has been initiated. You can monitor the progress
                    through the server's IPMI console.
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <div className="flex items-start">
                <AlertTriangle className="w-4 h-4 mr-2 text-red-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-red-800">{error}</div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-4 border-t">
          {installationInProgress ? (
            /* Only show close button when installation is already in progress */
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md text-sm hover:bg-indigo-700"
            >
              Close
            </button>
          ) : !isInstalling ? (
            <>
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
                disabled={loading}
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleStartReinstall}
                className="px-4 py-2 bg-red-600 text-white rounded-md text-sm hover:bg-red-700 flex items-center"
                disabled={loading || !selectedOS || (!server?.ipmi || !server?.ipmi_root_pass)}
              >
                {loading ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Starting...
                  </>
                ) : (
                  <>
                    Start Reinstall
                  </>
                )}
              </button>
            </>
          ) : (
            !loading && (
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 bg-indigo-600 text-white rounded-md text-sm hover:bg-indigo-700"
              >
                Close
              </button>
            )
          )}
        </div>
      </div>
    </div>
  );
};

export default PXEReinstallModal; 